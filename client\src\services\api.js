/**
 * API Service for VWork Platform
 * Handles all HTTP requests to the backend server
 */

// Get API base URL from environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';
const API_VERSION = 'v1';
const API_URL = `${API_BASE_URL}/api/${API_VERSION}`;

console.log('🔗 API Configuration:', {
  baseUrl: API_BASE_URL,
  apiUrl: API_URL,
  environment: process.env.NODE_ENV,
  reactAppApiUrl: process.env.REACT_APP_API_URL,
  currentHost: window.location.host
});

/**
 * Get authentication headers
 * @returns {object} Headers with auth tokens
 */
const getAuthHeaders = () => {
  const headers = {
    'Content-Type': 'application/json',
  };

  // Add auth token if available
  const authToken = localStorage.getItem('auth_token');
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }

  return headers;
};

/**
 * Generic API request function
 * @param {string} endpoint - API endpoint (without /api/v1 prefix)
 * @param {object} options - Fetch options
 * @returns {Promise} - API response
 */
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_URL}${endpoint}`;
  
  const defaultOptions = {
    headers: {
      ...getAuthHeaders(),
      ...options.headers,
    },
    credentials: 'include', // Include cookies for authentication
    ...options,
  };

  try {
    console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
    
    const response = await fetch(url, defaultOptions);
    
    // Handle non-JSON responses (like health check)
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      console.log(`📄 API Response (text): ${response.status} - ${text}`);
      return { success: response.ok, data: text, status: response.status };
    }

    // Try to parse JSON with better error handling
    let data;
    try {
      const responseText = await response.text();
      console.log(`📄 Raw response: ${responseText.substring(0, 200)}...`);

      if (!responseText.trim()) {
        throw new Error('Empty response body');
      }

      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error(`❌ JSON Parse Error: ${parseError.message}`);
      throw new Error(`Invalid JSON response: ${parseError.message}`);
    }
    
    if (!response.ok) {
      console.error(`❌ API Error: ${response.status}`, data);
      
      // Handle authentication errors
      if (response.status === 401) {
        localStorage.removeItem('backend_token');
        console.log('🔒 Backend token expired, removed from storage');
      }
      
      throw new Error(data.message || `HTTP ${response.status}`);
    }

    console.log(`✅ API Success: ${response.status}`, data);
    return { success: true, data, status: response.status };
    
  } catch (error) {
    console.error(`🚨 API Request Failed: ${url}`, error);
    throw error;
  }
};

// API Service object with all endpoints
export const apiService = {
  // Health check
  healthCheck: () => apiRequest('/health'),

  // Authentication endpoints
  auth: {
    register: (userData) => apiRequest('/auth/register-direct', {
      method: 'POST',
      body: JSON.stringify(userData),
    }),
    login: (credentials) => apiRequest('/auth/login-direct', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }),
    logout: () => apiRequest('/auth/logout', { method: 'POST' }),
    getCurrentUser: () => apiRequest('/auth/me'),
  },

  // User endpoints
  users: {
    getProfile: (userId) => apiRequest(`/users/${userId}`),
    updateProfile: (userId, profileData) => apiRequest(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(profileData),
    }),
    getFreelancers: (filters = {}) => {
      const queryParams = new URLSearchParams(filters).toString();
      return apiRequest(`/freelancers${queryParams ? `?${queryParams}` : ''}`);
    },
  },

  // Project endpoints
  projects: {
    getAll: (filters = {}) => {
      const queryParams = new URLSearchParams(filters).toString();
      return apiRequest(`/projects${queryParams ? `?${queryParams}` : ''}`);
    },
    getById: (projectId) => apiRequest(`/projects/${projectId}`),
    create: (projectData) => apiRequest('/projects', {
      method: 'POST',
      body: JSON.stringify(projectData),
    }),
    update: (projectId, projectData) => apiRequest(`/projects/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(projectData),
    }),
    delete: (projectId) => apiRequest(`/projects/${projectId}`, {
      method: 'DELETE',
    }),
  },

  // Jobs endpoints
  jobs: {
    getAll: (filters = {}) => {
      const queryParams = new URLSearchParams(filters).toString();
      return apiRequest(`/jobs${queryParams ? `?${queryParams}` : ''}`);
    },
    getById: (jobId) => apiRequest(`/jobs/${jobId}`),
    create: (jobData) => apiRequest('/jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    }),
    update: (jobId, jobData) => apiRequest(`/jobs/${jobId}`, {
      method: 'PUT',
      body: JSON.stringify(jobData),
    }),
    delete: (jobId) => apiRequest(`/jobs/${jobId}`, {
      method: 'DELETE',
    }),
    apply: (jobId, applicationData) => apiRequest(`/jobs/${jobId}/apply`, {
      method: 'POST',
      body: JSON.stringify(applicationData),
    }),
  },

  // Chatbot endpoints
  chatbot: {
    sendMessage: (message) => apiRequest('/chatbot/chat', {
      method: 'POST',
      body: JSON.stringify({ message }),
    }),
    getHistory: () => apiRequest('/chatbot/history'),
  },

  // Messages endpoints
  messages: {
    getConversations: () => apiRequest('/messages/conversations'),
    getMessages: (conversationId) => apiRequest(`/messages/${conversationId}`),
    sendMessage: (conversationId, messageData) => apiRequest(`/messages/${conversationId}`, {
      method: 'POST',
      body: JSON.stringify(messageData),
    }),
  },

  // Community endpoints
  community: {
    getPosts: (filters = {}) => {
      const queryParams = new URLSearchParams(filters).toString();
      return apiRequest(`/community/posts${queryParams ? `?${queryParams}` : ''}`);
    },
    createPost: (postData) => apiRequest('/community/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    }),
  },

  // Upload endpoints
  upload: {
    uploadFile: (file, type = 'general') => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      
      return apiRequest('/upload', {
        method: 'POST',
        body: formData,
        headers: {}, // Remove Content-Type to let browser set it for FormData
      });
    },
  },

  // Notifications endpoints
  notifications: {
    getAll: () => apiRequest('/notifications'),
    markAsRead: (notificationId) => apiRequest(`/notifications/${notificationId}/read`, {
      method: 'PUT',
    }),
    markAllAsRead: () => apiRequest('/notifications/read-all', {
      method: 'PUT',
    }),
  },
};

// Test API connection
export const testApiConnection = async () => {
  try {
    console.log('🔍 Testing API connection...');
    const result = await apiService.healthCheck();
    console.log('✅ API connection successful:', result);
    return true;
  } catch (error) {
    console.error('❌ API connection failed:', error);
    return false;
  }
};

// Export API URL for other components
export { API_BASE_URL, API_URL };

export default apiService;
