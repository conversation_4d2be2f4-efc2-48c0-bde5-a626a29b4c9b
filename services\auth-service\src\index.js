const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import Firebase Admin directly
const admin = require('firebase-admin');

// Import local utilities
const { responseMiddleware, validateBody, schemas } = require('./utils');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  try {
    // In production, use service account key
    if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });
    } else {
      // In development, use default credentials
      admin.initializeApp({
        projectId: process.env.FIREBASE_PROJECT_ID || 'vwork-platform'
      });
    }
    console.log('✅ Firebase Admin initialized');
  } catch (error) {
    console.error('❌ Firebase Admin initialization failed:', error.message);
  }
}

const app = express();
const PORT = process.env.PORT || 3001;

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'vwork-default-secret-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// JWT Helper Functions
const generateToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

const verifyToken = (token) => {
  return jwt.verify(token, JWT_SECRET);
};

// In-memory user store (replace with database in production)
const users = new Map();

// Helper function to find user by email
const findUserByEmail = (email) => {
  for (const [uid, user] of users.entries()) {
    if (user.email === email) {
      return { uid, ...user };
    }
  }
  return null;
};

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Simple token verification middleware
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // Verify the Firebase ID token
    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // Add user info to request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name,
      picture: decodedToken.picture,
      firebaseUser: decodedToken
    };

    console.log(`🔐 Authenticated user: ${req.user.email} (${req.user.uid})`);
    next();

  } catch (error) {
    console.error('🚨 Token verification failed:', error.message);
    
    if (error.code === 'auth/id-token-expired') {
      return res.apiUnauthorized('Token has expired');
    } else if (error.code === 'auth/id-token-revoked') {
      return res.apiUnauthorized('Token has been revoked');
    } else if (error.code === 'auth/invalid-id-token') {
      return res.apiUnauthorized('Invalid token format');
    } else {
      return res.apiUnauthorized('Token verification failed');
    }
  }
};

// JWT token verification middleware
const verifyJWTToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.apiUnauthorized('No token provided');
    }

    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // Verify the JWT token
    const decoded = verifyToken(token);

    // Add user info to request
    req.user = {
      uid: decoded.uid,
      email: decoded.email,
      name: decoded.name,
      userType: decoded.userType,
      emailVerified: decoded.emailVerified
    };

    console.log(`🔐 Authenticated user: ${req.user.email} (${req.user.uid})`);
    next();
  } catch (error) {
    console.error('❌ JWT Token verification failed:', error.message);
    return res.apiUnauthorized('Invalid or expired token');
  }
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Auth Service',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// New JWT-based Auth routes
app.post('/auth/register-direct', async (req, res) => {
  try {
    const { email, password, name, userType, guildSpecialization } = req.body;

    console.log('🔐 Direct registration request for:', email);

    // Validate input
    if (!email || !password || !name || !userType) {
      return res.apiBadRequest('Missing required fields: email, password, name, userType');
    }

    // Check if user already exists
    const existingUser = findUserByEmail(email);
    if (existingUser) {
      return res.apiBadRequest('User already exists with this email');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate unique user ID
    const uid = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create user object
    const userData = {
      email,
      password: hashedPassword,
      name,
      userType,
      guildSpecialization: guildSpecialization || null,
      emailVerified: false, // In production, implement email verification
      createdAt: new Date().toISOString(),
      lastLoginAt: null
    };

    // Store user (in production, save to database)
    users.set(uid, userData);

    // Generate JWT token
    const token = generateToken({
      uid,
      email,
      name,
      userType,
      emailVerified: userData.emailVerified
    });

    console.log('✅ Direct registration successful:', email);

    res.apiSuccess({
      user: {
        uid,
        email,
        name,
        userType,
        emailVerified: userData.emailVerified,
        guildSpecialization: userData.guildSpecialization
      },
      token
    }, 'Registration successful');

  } catch (error) {
    console.error('❌ Direct registration failed:', error);
    res.apiError('Registration failed', 'REGISTRATION_ERROR');
  }
});

app.post('/auth/login-direct', async (req, res) => {
  try {
    const { email, password } = req.body;

    console.log('🔐 Direct login request for:', email);

    // Validate input
    if (!email || !password) {
      return res.apiBadRequest('Missing email or password');
    }

    // Find user
    const user = findUserByEmail(email);
    if (!user) {
      return res.apiUnauthorized('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.apiUnauthorized('Invalid email or password');
    }

    // Update last login
    user.lastLoginAt = new Date().toISOString();
    users.set(user.uid, user);

    // Generate JWT token
    const token = generateToken({
      uid: user.uid,
      email: user.email,
      name: user.name,
      userType: user.userType,
      emailVerified: user.emailVerified
    });

    console.log('✅ Direct login successful:', email);

    res.apiSuccess({
      user: {
        uid: user.uid,
        email: user.email,
        name: user.name,
        userType: user.userType,
        emailVerified: user.emailVerified,
        guildSpecialization: user.guildSpecialization
      },
      token
    }, 'Login successful');

  } catch (error) {
    console.error('❌ Direct login failed:', error);
    res.apiError('Login failed', 'LOGIN_ERROR');
  }
});

// Protected route to get current user
app.get('/auth/me', verifyJWTToken, async (req, res) => {
  try {
    const user = users.get(req.user.uid);
    if (!user) {
      return res.apiNotFound('User not found');
    }

    res.apiSuccess({
      uid: req.user.uid,
      email: user.email,
      name: user.name,
      userType: user.userType,
      emailVerified: user.emailVerified,
      guildSpecialization: user.guildSpecialization
    }, 'User data retrieved');

  } catch (error) {
    console.error('❌ Get user failed:', error);
    res.apiError('Failed to get user data', 'GET_USER_ERROR');
  }
});

// Legacy Firebase-based Auth routes (for backward compatibility)
app.post('/auth/register', validateBody(schemas.userRegistration), async (req, res) => {
  try {
    const { firebaseToken, userType, name, guildSpecialization } = req.body;

    console.log('🔐 Registration request:', { userType, name });

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);

    // Create user data
    const userData = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: name || decodedToken.name,
      userType,
      guildSpecialization: guildSpecialization || '',
      emailVerified: decodedToken.email_verified,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      profile: {
        bio: '',
        avatar: decodedToken.picture || null,
        location: {
          country: '',
          city: '',
          timezone: ''
        },
        website: '',
        phoneNumber: '',
        skills: [],
        hourlyRate: userType === 'freelancer' ? 0 : null,
        availability: userType === 'freelancer' ? 'available' : null,
        isComplete: false
      }
    };

    // TODO: Save to database
    console.log('✅ User registered:', userData);

    res.apiSuccess(userData, 'Registration successful');

  } catch (error) {
    console.error('❌ Registration failed:', error);

    if (error.code && error.code.startsWith('auth/')) {
      res.apiUnauthorized('Invalid Firebase token');
    } else {
      res.apiError('Registration failed', 'REGISTRATION_ERROR', 500);
    }
  }
});

app.post('/auth/login', validateBody(schemas.userLogin), async (req, res) => {
  try {
    const { firebaseToken } = req.body;

    console.log('🔐 Login request');

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);

    // TODO: Get user from database
    const userData = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name,
      emailVerified: decodedToken.email_verified,
      lastLoginAt: new Date().toISOString()
    };

    console.log('✅ Login successful:', userData.email);

    res.apiSuccess(userData, 'Login successful');

  } catch (error) {
    console.error('❌ Login failed:', error);

    if (error.code && error.code.startsWith('auth/')) {
      res.apiUnauthorized('Invalid Firebase token');
    } else {
      res.apiError('Login failed', 'LOGIN_ERROR', 500);
    }
  }
});

app.get('/auth/me', verifyFirebaseToken, async (req, res) => {
  try {
    // TODO: Get full user data from database
    const userData = {
      uid: req.user.uid,
      email: req.user.email,
      name: req.user.name,
      emailVerified: req.user.emailVerified,
      picture: req.user.picture
    };

    res.apiSuccess(userData, 'User data retrieved');

  } catch (error) {
    console.error('❌ Get user failed:', error);
    res.apiError('Failed to get user data', 'GET_USER_ERROR', 500);
  }
});

app.post('/auth/logout', verifyFirebaseToken, async (req, res) => {
  try {
    // TODO: Invalidate any backend tokens/sessions
    console.log('🔐 Logout:', req.user.email);

    res.apiSuccess(null, 'Logout successful');

  } catch (error) {
    console.error('❌ Logout failed:', error);
    res.apiError('Logout failed', 'LOGOUT_ERROR', 500);
  }
});

// Token verification endpoint for other services
app.post('/auth/verify', async (req, res) => {
  try {
    const { firebaseToken } = req.body;

    if (!firebaseToken) {
      return res.apiUnauthorized('No token provided');
    }

    console.log('🔐 Token verification request');

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);

    const userData = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name,
      emailVerified: decodedToken.email_verified,
      picture: decodedToken.picture
    };

    console.log('✅ Token verified:', userData.email);

    res.apiSuccess(userData, 'Token verified successfully');

  } catch (error) {
    console.error('❌ Token verification failed:', error);

    if (error.code && error.code.startsWith('auth/')) {
      res.apiUnauthorized('Invalid token');
    } else {
      res.apiError('Token verification failed', 'VERIFY_ERROR', 500);
    }
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Auth Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`🔐 Auth Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});