{"name": "vwork-frontend", "version": "1.0.0", "description": "VWork - Modern Freelancer Marketplace Frontend", "private": true, "homepage": ".", "scripts": {"start": "cross-env GENERATE_SOURCEMAP=false BROWSER=none react-scripts start", "start:fresh": "npm run clear-cache && cross-env GENERATE_SOURCEMAP=false BROWSER=none REACT_APP_DISABLE_CACHE=true react-scripts start", "build": "react-scripts build", "build:production": "chmod +x build-production.sh && ./build-production.sh", "build:production:windows": "build-production.bat", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "npm run start:fresh", "setup": "node ../scripts/setup-env.js", "test-env": "node test-env.js", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "lint:production": "eslint src --ext .js,.jsx,.ts,.tsx --config .eslintrc.production.json", "quality:check": "npm run lint && npm run type-check", "quality:fix": "npm run lint:fix", "quality:production": "npm run lint:production && npm run type-check", "prepare": "node -e \"if (process.env.NODE_ENV !== 'production' && !process.env.CI) { try { require('husky').install() } catch (e) {} }\"", "type-check": "tsc --noEmit", "clean": "rm -rf build node_modules package-lock.json && npm install", "clear-cache": "npm run clear-cache:windows || npm run clear-cache:unix", "clear-cache:windows": "if exist node_modules\\.cache rmdir /s /q node_modules\\.cache && echo Cache cleared successfully!", "clear-cache:unix": "rm -rf node_modules/.cache && echo Cache cleared successfully!", "preview": "serve -s build", "analyze": "npm run build && npx bundle-analyzer"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@iconify/icons-simple-icons": "^1.2.74", "@iconify/react": "^6.0.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.14", "axios": "^1.5.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "emoji-js": "^3.8.1", "emoji-mart": "^5.6.0", "framer-motion": "^10.16.0", "gsap": "^3.13.0", "lucide-react": "^0.263.1", "postcss": "^8.4.27", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.2", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "simple-icons": "^15.4.0", "swiper": "^10.3.1", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.3", "twemoji": "^14.0.2"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "is-ci": "^3.0.1", "lint-staged": "^16.1.2", "prop-types": "^15.8.1", "typescript": "^4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix"]}}