{"name": "auth-service", "version": "1.0.0", "description": "Authentication service for VWork platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "build": "npm ci --production", "dev": "nodemon src/index.js", "test": "jest", "postinstall": "echo 'Auth service dependencies installed successfully'"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^13.4.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}