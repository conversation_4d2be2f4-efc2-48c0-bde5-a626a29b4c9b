{"name": "vwork-platform", "version": "1.0.0", "description": "VWork - Comprehensive Freelancing Platform", "main": "scripts/unified-start.js", "scripts": {"start": "node scripts/start-all.js", "dev": "node scripts/start-all.js", "setup": "node scripts/setup-all.js", "stop": "node scripts/stop.js", "status": "node scripts/status.js", "test": "node scripts/test-api.js", "deploy:render": "node scripts/deploy-render-manual.js", "setup:render": "node scripts/setup-render-env.js", "build:client": "cd client && npm run build", "build:production": "node scripts/build-production.js", "install:deps": "node scripts/install-dependencies.js", "fix:axios": "node scripts/fix-axios-error.js", "test:simple": "node scripts/test-services-simple.js"}, "keywords": ["freelancing", "platform", "microservices", "react", "nodejs", "firebase"], "author": "VWork Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/VinkRasengan/Vwork.git"}, "bugs": {"url": "https://github.com/VinkRasengan/Vwork/issues"}, "homepage": "https://github.com/VinkRasengan/Vwork#readme", "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.54.0", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.0"}}